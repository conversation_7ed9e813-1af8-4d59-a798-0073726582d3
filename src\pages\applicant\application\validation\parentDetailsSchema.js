import * as yup from 'yup';
import { t } from 'i18next';
import { AADHAAR, MASKED_AADHAAR, MOBILE } from 'common/regex';
import {
  CARE_STATUS, CURRENT_CARE_PROVIDER, RELATIONSHIP
} from '../constants';

export const parentDetailsSchema = yup.object().shape({
  // Applicant care status
  applicantCareStatus: yup
    .string()
    .required(t('fieldSelectOption', { field: t('applicantCareStatus') })),

  // Current care provider (for orphan scenario)
  currentCareProvider: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.ORPHAN,
      then: (schema) => schema.required(t('fieldRequired', { field: t('currentCareProvider') })),
      otherwise: (schema) => schema.notRequired()
    }),

  // Father Details (for both parents scenario)
  fatherName: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('name') }))
        .min(2, t('mustBeAtLeast', { type: t('name'), count: 2, unit: 'characters' }))
        .max(50, t('fieldMaxLength', { field: t('name'), max: 50 })),
      otherwise: (schema) => schema.notRequired()
    }),

  fatherRelationshipToApplicant: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema.required(t('fieldRequired', { field: t('relationshipToApplicant') })),
      otherwise: (schema) => schema.notRequired()
    }),

  fatherAadhaarNumber: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('aadhaarNumber') }))
        .test(
          'aadhaar-format',
          t('fieldValidFormat', { field: t('aadhaarNumber') }),
          (value) => {
            if (!value) return false;
            return AADHAAR.test(value) || MASKED_AADHAAR.test(value);
          }
        ),
      otherwise: (schema) => schema.notRequired()
    }),

  fatherContactNumber: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('contactNumber') }))
        .matches(MOBILE, t('fieldValidMobile')),
      otherwise: (schema) => schema.notRequired()
    }),

  // Mother Details (for both parents scenario)
  motherName: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('name') }))
        .min(2, t('mustBeAtLeast', { type: t('name'), count: 2, unit: 'characters' }))
        .max(50, t('fieldMaxLength', { field: t('name'), max: 50 })),
      otherwise: (schema) => schema.notRequired()
    }),

  motherRelationshipToApplicant: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema.required(t('fieldRequired', { field: t('relationshipToApplicant') })),
      otherwise: (schema) => schema.notRequired()
    }),

  motherAadhaarNumber: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('aadhaarNumber') }))
        .test(
          'aadhaar-format',
          t('fieldValidFormat', { field: t('aadhaarNumber') }),
          (value) => {
            if (!value) return false;
            return AADHAAR.test(value) || MASKED_AADHAAR.test(value);
          }
        ),
      otherwise: (schema) => schema.notRequired()
    }),

  motherContactNumber: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.PARENTS,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('contactNumber') }))
        .matches(MOBILE, t('fieldValidMobile')),
      otherwise: (schema) => schema.notRequired()
    }),

  // Single Parent/Guardian Details (for single parent scenario)
  parentName: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.SINGLE_PARENT,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('parentName') }))
        .min(2, t('mustBeAtLeast', { type: t('parentName'), count: 2, unit: 'characters' }))
        .max(50, t('fieldMaxLength', { field: t('parentName'), max: 50 })),
      otherwise: (schema) => schema.notRequired()
    }),

  relationshipToApplicant: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.SINGLE_PARENT,
      then: (schema) => schema.required(t('fieldRequired', { field: t('relationshipToApplicant') })),
      otherwise: (schema) => schema.notRequired()
    }),

  aadhaarNumber: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.SINGLE_PARENT,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('aadhaarNumber') }))
        .test(
          'aadhaar-format',
          t('fieldValidFormat', { field: t('aadhaarNumber') }),
          (value) => {
            if (!value) return false;
            return AADHAAR.test(value) || MASKED_AADHAAR.test(value);
          }
        ),
      otherwise: (schema) => schema.notRequired()
    }),

  contactNumber: yup
    .string()
    .when('applicantCareStatus', {
      is: CARE_STATUS.SINGLE_PARENT,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('contactNumber') }))
        .matches(MOBILE, t('fieldValidMobile')),
      otherwise: (schema) => schema.notRequired()
    }),

  // Guardian Details (for orphan with guardian scenario)
  guardianName: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.GUARDIAN,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('name') }))
        .min(2, t('mustBeAtLeast', { type: t('name'), count: 2, unit: 'characters' }))
        .max(50, t('fieldMaxLength', { field: t('name'), max: 50 })),
      otherwise: (schema) => schema.notRequired()
    }),

  guardianRelationshipToApplicant: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.GUARDIAN,
      then: (schema) => schema.required(t('fieldRequired', { field: t('relationshipToApplicant') })),
      otherwise: (schema) => schema.notRequired()
    }),

  guardianAadhaarNumber: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.GUARDIAN,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('aadhaarNumber') }))
        .test(
          'aadhaar-format',
          t('fieldValidFormat', { field: t('aadhaarNumber') }),
          (value) => {
            if (!value) return false;
            return AADHAAR.test(value) || MASKED_AADHAAR.test(value);
          }
        ),
      otherwise: (schema) => schema.notRequired()
    }),

  guardianContactNumber: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.GUARDIAN,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('contactNumber') }))
        .matches(MOBILE, t('fieldValidMobile')),
      otherwise: (schema) => schema.notRequired()
    }),

  // Institution Details (for orphan with institution scenario)
  institutionName: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.INSTITUTION,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('nameOfInstitution') }))
        .min(2, t('mustBeAtLeast', { type: t('nameOfInstitution'), count: 2, unit: 'characters' }))
        .max(100, t('fieldMaxLength', { field: t('nameOfInstitution'), max: 100 })),
      otherwise: (schema) => schema.notRequired()
    }),

  institutionRegistrationNumber: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.INSTITUTION,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('institutionRegistrationNumber') }))
        .min(2, t('mustBeAtLeast', { type: t('institutionRegistrationNumber'), count: 2, unit: 'characters' })),
      otherwise: (schema) => schema.notRequired()
    }),

  institutionContactNumber: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.INSTITUTION,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('institutionContactNumber') }))
        .matches(MOBILE, t('fieldValidMobile')),
      otherwise: (schema) => schema.notRequired()
    }),

  institutionAddress: yup
    .string()
    .when(['applicantCareStatus', 'currentCareProvider'], {
      is: (careStatus, careProvider) => careStatus === CARE_STATUS.ORPHAN
        && careProvider === CURRENT_CARE_PROVIDER.INSTITUTION,
      then: (schema) => schema
        .required(t('fieldRequired', { field: t('institutionAddress') }))
        .min(2, t('mustBeAtLeast', { type: t('institutionAddress'), count: 2, unit: 'characters' }))
        .max(200, t('fieldMaxLength', { field: t('institutionAddress'), max: 200 })),
      otherwise: (schema) => schema.notRequired()
    }),

  // Financial Details
  annualFamilyIncome: yup
    .number()
    .nullable()
    .transform((value, originalValue) => {
      return originalValue === '' ? null : value;
    })
    .required(t('fieldRequired', { field: t('annualFamilyIncome') }))
    .min(0, t('fieldMinValue', { field: t('annualFamilyIncome'), min: 0 }))
    .max(250000, t('fieldMaxValue', { field: t('annualFamilyIncome'), max: '2.5 lakhs' })),

  incomeCertificateIssuedBy: yup
    .string()
    .required(t('fieldSelectOption', { field: t('incomeCertificateIssuedBy') })),

  incomeCertificateNo: yup
    .string()
    .required(t('fieldRequired', { field: t('incomeCertificateNo') }))
    .min(2, t('mustBeAtLeast', { type: t('incomeCertificateNo'), count: 2, unit: 'characters' })),

  certificateIssuedDate: yup
    .date()
    .required(t('fieldRequired', { field: t('certificateIssuedDate') }))
    .max(new Date(), t('fieldFutureDate', { field: t('certificateIssuedDate') }))
});

export const PARENT_DETAILS_DEFAULT_VALUES = {
  applicantCareStatus: '',
  currentCareProvider: '',
  // Father fields (for both parents scenario)
  fatherName: '',
  fatherRelationshipToApplicant: RELATIONSHIP.FATHER,
  fatherAadhaarNumber: '',
  fatherContactNumber: '',
  // Mother fields (for both parents scenario)
  motherName: '',
  motherRelationshipToApplicant: RELATIONSHIP.MOTHER,
  motherAadhaarNumber: '',
  motherContactNumber: '',
  // Single parent fields (for single parent scenario)
  parentName: '',
  relationshipToApplicant: '',
  aadhaarNumber: '',
  contactNumber: '',
  // Guardian fields (for orphan with guardian scenario)
  guardianName: '',
  guardianRelationshipToApplicant: '',
  guardianAadhaarNumber: '',
  guardianContactNumber: '',
  // Institution fields (for orphan with institution scenario)
  institutionName: '',
  institutionRegistrationNumber: '',
  institutionContactNumber: '',
  institutionAddress: '',
  // Financial details
  annualFamilyIncome: null,
  incomeCertificateIssuedBy: '',
  incomeCertificateNo: '',
  certificateIssuedDate: null
};
